{"snippets": [{"id": 0, "content": "    @Override\n    public UserDetails loadUserByUsername(String username){\n        //获取用户信息\n        UmsAdmin admin = getAdminByUsername(username);\n        if (admin != null) {\n            List<UmsResource> resourceList = getResourceList(admin.getId());\n            return new AdminUserDetails(admin,resourceList);\n        }\n        throw new UsernameNotFoundException(\"用户名或密码错误\");\n    }\n"}, {"id": 1, "content": "    @Bean\n    public UserDetailsService userDetailsService() {\n        //获取登录用户信息\n        return new UserDetailsService() {\n            @Override\n            public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {\n                UmsAdminExample example = new UmsAdminExample();\n                example.createCriteria().andUsernameEqualTo(username);\n                List<UmsAdmin> umsAdminList = umsAdminMapper.selectByExample(example);\n                if (umsAdminList != null && umsAdminList.size() > 0) {\n                    return new AdminUserDetails(umsAdminList.get(0));\n                }\n                throw new UsernameNotFoundException(\"用户名或密码错误\");\n            }\n        };\n    }\n"}, {"id": 2, "content": "/**\n * SpringSecurity需要的用户信息封装类\n * Created by macro on 2018/4/26.\n */\npublic class AdminUserDetails implements UserDetails {\n    //后台用户\n    private final UmsAdmin umsAdmin;\n    //拥有资源列表\n    private final List<UmsResource> resourceList;\n\n    public AdminUserDetails(UmsAdmin umsAdmin,List<UmsResource> resourceList) {\n        this.umsAdmin = umsAdmin;\n        this.resourceList = resourceList;\n    }\n\n    @Override\n    public Collection<? extends GrantedAuthority> getAuthorities() {\n        //返回当前用户所拥有的资源\n        return resourceList.stream()\n                .map(resource ->new SimpleGrantedAuthority(resource.getId()+\":\"+resource.getName()))\n                .collect(Collectors.toList());\n    }\n\n    @Override\n    public String getPassword() {\n        return umsAdmin.getPassword();\n    }\n\n    @Override\n    public String getUsername() {\n        return umsAdmin.getUsername();\n    }\n\n    @Override\n    public boolean isAccountNonExpired() {\n        return true;\n    }\n\n    @Override\n    public boolean isAccountNonLocked() {\n        return true;\n    }\n\n    @Override\n    public boolean isCredentialsNonExpired() {\n        return true;\n    }\n\n    @Override\n    public boolean isEnabled() {\n        return umsAdmin.getStatus().equals(1);\n    }\n}\n"}, {"id": 3, "content": "/**\n * 后台用户管理Service\n * Created by macro on 2018/4/26.\n */\npublic interface UmsAdminService {\n    /**\n     * 根据用户名获取后台管理员\n     */\n    UmsAdmin getAdminByUsername(String username);\n\n    /**\n     * 注册功能\n     */\n    UmsAdmin register(UmsAdminParam umsAdminParam);\n\n    /**\n     * 登录功能\n     * @param username 用户名\n     * @param password 密码\n     * @return 生成的JWT的token\n     */\n    String login(String username,String password);\n\n    /**\n     * 刷新token的功能\n     * @param oldToken 旧的token\n     */\n    String refreshToken(String oldToken);\n\n    /**\n     * 根据用户id获取用户\n     */\n    UmsAdmin getItem(Long id);\n\n    /**\n     * 根据用户名或昵称分页查询用户\n     */\n    List<UmsAdmin> list(String keyword, Integer pageSize, Integer pageNum);\n\n    /**\n     * 修改指定用户信息\n     */\n    int update(Long id, UmsAdmin admin);\n\n    /**\n     * 删除指定用户\n     */\n    int delete(Long id);\n\n    /**\n     * 修改用户角色关系\n     */\n    @Transactional\n    int updateRole(Long adminId, List<Long> roleIds);\n\n    /**\n     * 获取用户对应角色\n     */\n    List<UmsRole> getRoleList(Long adminId);\n\n    /**\n     * 获取指定用户的可访问资源\n     */\n    List<UmsResource> getResourceList(Long adminId);\n\n    /**\n     * 修改密码\n     */\n    int updatePassword(UpdateAdminPasswordParam updatePasswordParam);\n\n    /**\n     * 获取用户信息\n     */\n    UserDetails loadUserByUsername(String username);\n\n    /**\n     * 获取缓存服务\n     */\n    UmsAdminCacheService getCacheService();\n\n    /**\n     * 登出功能\n     * @param username 用户名\n     */\n    void logout(String username);\n}\n"}, {"id": 4, "content": "/**\n * SpringSecurity需要的用户详情\n * Created by macro on 2018/4/26.\n */\npublic class AdminUserDetails implements UserDetails {\n    private UmsAdmin umsAdmin;\n\n    public AdminUserDetails(UmsAdmin umsAdmin) {\n        this.umsAdmin = umsAdmin;\n    }\n\n    @Override\n    public Collection<? extends GrantedAuthority> getAuthorities() {\n        //返回当前用户的权限\n        return Arrays.asList(new SimpleGrantedAuthority(\"TEST\"));\n    }\n\n    @Override\n    public String getPassword() {\n        return umsAdmin.getPassword();\n    }\n\n    @Override\n    public String getUsername() {\n        return umsAdmin.getUsername();\n    }\n\n    @Override\n    public boolean isAccountNonExpired() {\n        return true;\n    }\n\n    @Override\n    public boolean isAccountNonLocked() {\n        return true;\n    }\n\n    @Override\n    public boolean isCredentialsNonExpired() {\n        return true;\n    }\n\n    @Override\n    public boolean isEnabled() {\n        return true;\n    }\n}\n"}, {"id": 5, "content": "    @Override\n    public UserDetails loadUserByUsername(String username) {\n        UmsMember member = getByUsername(username);\n        if(member!=null){\n            return new MemberDetails(member);\n        }\n        throw new UsernameNotFoundException(\"用户名或密码错误\");\n    }\n"}, {"id": 6, "content": "/**\n * 会员管理Service\n * Created by macro on 2018/8/3.\n */\npublic interface UmsMemberService {\n    /**\n     * 根据用户名获取会员\n     */\n    UmsMember getByUsername(String username);\n\n    /**\n     * 根据会员编号获取会员\n     */\n    UmsMember getById(Long id);\n\n    /**\n     * 用户注册\n     */\n    @Transactional\n    void register(String username, String password, String telephone, String authCode);\n\n    /**\n     * 生成验证码\n     */\n    String generateAuthCode(String telephone);\n\n    /**\n     * 修改密码\n     */\n    @Transactional\n    void updatePassword(String telephone, String password, String authCode);\n\n    /**\n     * 获取当前登录会员\n     */\n    UmsMember getCurrentMember();\n\n    /**\n     * 根据会员id修改会员积分\n     */\n    void updateIntegration(Long id,Integer integration);\n\n\n    /**\n     * 获取用户信息\n     */\n    UserDetails loadUserByUsername(String username);\n\n    /**\n     * 登录后获取token\n     */\n    String login(String username, String password);\n\n    /**\n     * 刷新token\n     */\n    String refreshToken(String token);\n}\n"}, {"id": 7, "content": "/**\n * SpringSecurity需要的用户信息封装类\n * Created by macro on 2018/8/3.\n */\npublic class MemberDetails implements UserDetails {\n    private final UmsMember umsMember;\n\n    public MemberDetails(UmsMember umsMember) {\n        this.umsMember = umsMember;\n    }\n\n    @Override\n    public Collection<? extends GrantedAuthority> getAuthorities() {\n        //返回当前用户的权限\n        return Arrays.asList(new SimpleGrantedAuthority(\"TEST\"));\n    }\n\n    @Override\n    public String getPassword() {\n        return umsMember.getPassword();\n    }\n\n    @Override\n    public String getUsername() {\n        return umsMember.getUsername();\n    }\n\n    @Override\n    public boolean isAccountNonExpired() {\n        return true;\n    }\n\n    @Override\n    public boolean isAccountNonLocked() {\n        return true;\n    }\n\n    @Override\n    public boolean isCredentialsNonExpired() {\n        return true;\n    }\n\n    @Override\n    public boolean isEnabled() {\n        return umsMember.getStatus()==1;\n    }\n\n    public UmsMember getUmsMember() {\n        return umsMember;\n    }\n}\n"}, {"id": 8, "content": "    public String getNickName() {\n        return nickName;\n    }\n"}, {"id": 9, "content": "    @Override\n    public String getUsername() {\n        return umsAdmin.getUsername();\n    }\n"}, {"id": 10, "content": "    @Override\n    public String getUsername() {\n        return umsAdmin.getUsername();\n    }\n"}, {"id": 11, "content": "    public void setNickName(String nickName) {\n        this.nickName = nickName;\n    }\n"}, {"id": 12, "content": "    /**\n     * 添加登录记录\n     * @param username 用户名\n     */\n    private void insertLoginLog(String username) {\n        UmsAdmin admin = getAdminByUsername(username);\n        if(admin==null) return;\n        UmsAdminLoginLog loginLog = new UmsAdminLoginLog();\n        loginLog.setAdminId(admin.getId());\n        loginLog.setCreateTime(new Date());\n        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();\n        HttpServletRequest request = attributes.getRequest();\n        loginLog.setIp(RequestUtil.getRequestIp(request));\n        loginLogMapper.insert(loginLog);\n    }\n"}, {"id": 13, "content": "/**\n * 后台用户管理Service实现类\n * Created by macro on 2018/4/26.\n */\n@Service\npublic class UmsAdminServiceImpl implements UmsAdminService {\n    private static final Logger LOGGER = LoggerFactory.getLogger(UmsAdminServiceImpl.class);\n    @Autowired\n    private JwtTokenUtil jwtTokenUtil;\n    @Autowired\n    private PasswordEncoder passwordEncoder;\n    @Autowired\n    private UmsAdminMapper adminMapper;\n    @Autowired\n    private UmsAdminRoleRelationMapper adminRoleRelationMapper;\n    @Autowired\n    private UmsAdminRoleRelationDao adminRoleRelationDao;\n    @Autowired\n    private UmsAdminLoginLogMapper loginLogMapper;\n\n    @Override\n    public UmsAdmin getAdminByUsername(String username) {\n        //先从缓存中获取数据\n        UmsAdmin admin = getCacheService().getAdmin(username);\n        if (admin != null) return admin;\n        //缓存中没有从数据库中获取\n        UmsAdminExample example = new UmsAdminExample();\n        example.createCriteria().andUsernameEqualTo(username);\n        List<UmsAdmin> adminList = adminMapper.selectByExample(example);\n        if (adminList != null && adminList.size() > 0) {\n            admin = adminList.get(0);\n            //将数据库中的数据存入缓存中\n            getCacheService().setAdmin(admin);\n            return admin;\n        }\n        return null;\n    }\n\n    @Override\n    public UmsAdmin register(UmsAdminParam umsAdminParam) {\n        UmsAdmin umsAdmin = new UmsAdmin();\n        BeanUtils.copyProperties(umsAdminParam, umsAdmin);\n        umsAdmin.setCreateTime(new Date());\n        umsAdmin.setStatus(1);\n        //查询是否有相同用户名的用户\n        UmsAdminExample example = new UmsAdminExample();\n        example.createCriteria().andUsernameEqualTo(umsAdmin.getUsername());\n        List<UmsAdmin> umsAdminList = adminMapper.selectByExample(example);\n        if (umsAdminList.size() > 0) {\n            return null;\n        }\n        //将密码进行加密操作\n        String encodePassword = passwordEncoder.encode(umsAdmin.getPassword());\n        umsAdmin.setPassword(encodePassword);\n        adminMapper.insert(umsAdmin);\n        return umsAdmin;\n    }\n\n    @Override\n    public String login(String username, String password) {\n        String token = null;\n        //密码需要客户端加密后传递\n        try {\n            UserDetails userDetails = loadUserByUsername(username);\n            if(!passwordEncoder.matches(password,userDetails.getPassword())){\n                Asserts.fail(\"密码不正确\");\n            }\n            if(!userDetails.isEnabled()){\n                Asserts.fail(\"帐号已被禁用\");\n            }\n            UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(userDetails, null, userDetails.getAuthorities());\n            SecurityContextHolder.getContext().setAuthentication(authentication);\n            token = jwtTokenUtil.generateToken(userDetails);\n//            updateLoginTimeByUsername(username);\n            insertLoginLog(username);\n        } catch (AuthenticationException e) {\n            LOGGER.warn(\"登录异常:{}\", e.getMessage());\n        }\n        return token;\n    }\n\n    /**\n     * 添加登录记录\n     * @param username 用户名\n     */\n    private void insertLoginLog(String username) {\n        UmsAdmin admin = getAdminByUsername(username);\n        if(admin==null) return;\n        UmsAdminLoginLog loginLog = new UmsAdminLoginLog();\n        loginLog.setAdminId(admin.getId());\n        loginLog.setCreateTime(new Date());\n        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();\n        HttpServletRequest request = attributes.getRequest();\n        loginLog.setIp(RequestUtil.getRequestIp(request));\n        loginLogMapper.insert(loginLog);\n    }\n\n    /**\n     * 根据用户名修改登录时间\n     */\n    private void updateLoginTimeByUsername(String username) {\n        UmsAdmin record = new UmsAdmin();\n        record.setLoginTime(new Date());\n        UmsAdminExample example = new UmsAdminExample();\n        example.createCriteria().andUsernameEqualTo(username);\n        adminMapper.updateByExampleSelective(record, example);\n    }\n\n    @Override\n    public String refreshToken(String oldToken) {\n        return jwtTokenUtil.refreshHeadToken(oldToken);\n    }\n\n    @Override\n    public UmsAdmin getItem(Long id) {\n        return adminMapper.selectByPrimaryKey(id);\n    }\n\n    @Override\n    public List<UmsAdmin> list(String keyword, Integer pageSize, Integer pageNum) {\n        PageHelper.startPage(pageNum, pageSize);\n        UmsAdminExample example = new UmsAdminExample();\n        UmsAdminExample.Criteria criteria = example.createCriteria();\n        if (!StrUtil.isEmpty(keyword)) {\n            criteria.andUsernameLike(\"%\" + keyword + \"%\");\n            example.or(example.createCriteria().andNickNameLike(\"%\" + keyword + \"%\"));\n        }\n        return adminMapper.selectByExample(example);\n    }\n\n    @Override\n    public int update(Long id, UmsAdmin admin) {\n        admin.setId(id);\n        UmsAdmin rawAdmin = adminMapper.selectByPrimaryKey(id);\n        if(rawAdmin.getPassword().equals(admin.getPassword())){\n            //与原加密密码相同的不需要修改\n            admin.setPassword(null);\n        }else{\n            //与原加密密码不同的需要加密修改\n            if(StrUtil.isEmpty(admin.getPassword())){\n                admin.setPassword(null);\n            }else{\n                admin.setPassword(passwordEncoder.encode(admin.getPassword()));\n            }\n        }\n        int count = adminMapper.updateByPrimaryKeySelective(admin);\n        getCacheService().delAdmin(id);\n        return count;\n    }\n\n    @Override\n    public int delete(Long id) {\n        int count = adminMapper.deleteByPrimaryKey(id);\n        getCacheService().delAdmin(id);\n        getCacheService().delResourceList(id);\n        return count;\n    }\n\n    @Override\n    public int updateRole(Long adminId, List<Long> roleIds) {\n        int count = roleIds == null ? 0 : roleIds.size();\n        //先删除原来的关系\n        UmsAdminRoleRelationExample adminRoleRelationExample = new UmsAdminRoleRelationExample();\n        adminRoleRelationExample.createCriteria().andAdminIdEqualTo(adminId);\n        adminRoleRelationMapper.deleteByExample(adminRoleRelationExample);\n        //建立新关系\n        if (!CollectionUtils.isEmpty(roleIds)) {\n            List<UmsAdminRoleRelation> list = new ArrayList<>();\n            for (Long roleId : roleIds) {\n                UmsAdminRoleRelation roleRelation = new UmsAdminRoleRelation();\n                roleRelation.setAdminId(adminId);\n                roleRelation.setRoleId(roleId);\n                list.add(roleRelation);\n            }\n            adminRoleRelationDao.insertList(list);\n        }\n        getCacheService().delResourceList(adminId);\n        return count;\n    }\n\n    @Override\n    public List<UmsRole> getRoleList(Long adminId) {\n        return adminRoleRelationDao.getRoleList(adminId);\n    }\n\n    @Override\n    public List<UmsResource> getResourceList(Long adminId) {\n        //先从缓存中获取数据\n        List<UmsResource> resourceList = getCacheService().getResourceList(adminId);\n        if(CollUtil.isNotEmpty(resourceList)){\n            return  resourceList;\n        }\n        //缓存中没有从数据库中获取\n        resourceList = adminRoleRelationDao.getResourceList(adminId);\n        if(CollUtil.isNotEmpty(resourceList)){\n            //将数据库中的数据存入缓存中\n            getCacheService().setResourceList(adminId,resourceList);\n        }\n        return resourceList;\n    }\n\n    @Override\n    public int updatePassword(UpdateAdminPasswordParam param) {\n        if(StrUtil.isEmpty(param.getUsername())\n                ||StrUtil.isEmpty(param.getOldPassword())\n                ||StrUtil.isEmpty(param.getNewPassword())){\n            return -1;\n        }\n        UmsAdminExample example = new UmsAdminExample();\n        example.createCriteria().andUsernameEqualTo(param.getUsername());\n        List<UmsAdmin> adminList = adminMapper.selectByExample(example);\n        if(CollUtil.isEmpty(adminList)){\n            return -2;\n        }\n        UmsAdmin umsAdmin = adminList.get(0);\n        if(!passwordEncoder.matches(param.getOldPassword(),umsAdmin.getPassword())){\n            return -3;\n        }\n        umsAdmin.setPassword(passwordEncoder.encode(param.getNewPassword()));\n        adminMapper.updateByPrimaryKey(umsAdmin);\n        getCacheService().delAdmin(umsAdmin.getId());\n        return 1;\n    }\n\n    @Override\n    public UserDetails loadUserByUsername(String username){\n        //获取用户信息\n        UmsAdmin admin = getAdminByUsername(username);\n        if (admin != null) {\n            List<UmsResource> resourceList = getResourceList(admin.getId());\n            return new AdminUserDetails(admin,resourceList);\n        }\n        throw new UsernameNotFoundException(\"用户名或密码错误\");\n    }\n\n    @Override\n    public UmsAdminCacheService getCacheService() {\n        return SpringUtil.getBean(UmsAdminCacheService.class);\n    }\n\n    @Override\n    public void logout(String username) {\n        //清空缓存中的用户相关数据\n        UmsAdmin admin = getCacheService().getAdmin(username);\n        getCacheService().delAdmin(admin.getId());\n        getCacheService().delResourceList(admin.getId());\n    }\n}\n"}, {"id": 14, "content": "    @Override\n    public String getUsername() {\n        return umsMember.getUsername();\n    }\n"}, {"id": 15, "content": "/**\n * 后台用户管理Controller\n * Created by macro on 2018/4/26.\n */\n@Controller\n@Api(tags = \"UmsAdminController\")\n@Tag(name = \"UmsAdminController\", description = \"后台用户管理\")\n@RequestMapping(\"/admin\")\npublic class UmsAdminController {\n    @Value(\"${jwt.tokenHeader}\")\n    private String tokenHeader;\n    @Value(\"${jwt.tokenHead}\")\n    private String tokenHead;\n    @Autowired\n    private UmsAdminService adminService;\n    @Autowired\n    private UmsRoleService roleService;\n\n    @ApiOperation(value = \"用户注册\")\n    @RequestMapping(value = \"/register\", method = RequestMethod.POST)\n    @ResponseBody\n    public CommonResult<UmsAdmin> register(@Validated @RequestBody UmsAdminParam umsAdminParam) {\n        UmsAdmin umsAdmin = adminService.register(umsAdminParam);\n        if (umsAdmin == null) {\n            return CommonResult.failed();\n        }\n        return CommonResult.success(umsAdmin);\n    }\n\n    @ApiOperation(value = \"登录以后返回token\")\n    @RequestMapping(value = \"/login\", method = RequestMethod.POST)\n    @ResponseBody\n    public CommonResult login(@Validated @RequestBody UmsAdminLoginParam umsAdminLoginParam) {\n        String token = adminService.login(umsAdminLoginParam.getUsername(), umsAdminLoginParam.getPassword());\n        if (token == null) {\n            return CommonResult.validateFailed(\"用户名或密码错误\");\n        }\n        Map<String, String> tokenMap = new HashMap<>();\n        tokenMap.put(\"token\", token);\n        tokenMap.put(\"tokenHead\", tokenHead);\n        return CommonResult.success(tokenMap);\n    }\n\n    @ApiOperation(value = \"刷新token\")\n    @RequestMapping(value = \"/refreshToken\", method = RequestMethod.GET)\n    @ResponseBody\n    public CommonResult refreshToken(HttpServletRequest request) {\n        String token = request.getHeader(tokenHeader);\n        String refreshToken = adminService.refreshToken(token);\n        if (refreshToken == null) {\n            return CommonResult.failed(\"token已经过期！\");\n        }\n        Map<String, String> tokenMap = new HashMap<>();\n        tokenMap.put(\"token\", refreshToken);\n        tokenMap.put(\"tokenHead\", tokenHead);\n        return CommonResult.success(tokenMap);\n    }\n\n    @ApiOperation(value = \"获取当前登录用户信息\")\n    @RequestMapping(value = \"/info\", method = RequestMethod.GET)\n    @ResponseBody\n    public CommonResult getAdminInfo(Principal principal) {\n        if(principal==null){\n            return CommonResult.unauthorized(null);\n        }\n        String username = principal.getName();\n        UmsAdmin umsAdmin = adminService.getAdminByUsername(username);\n        Map<String, Object> data = new HashMap<>();\n        data.put(\"username\", umsAdmin.getUsername());\n        data.put(\"menus\", roleService.getMenuList(umsAdmin.getId()));\n        data.put(\"icon\", umsAdmin.getIcon());\n        List<UmsRole> roleList = adminService.getRoleList(umsAdmin.getId());\n        if(CollUtil.isNotEmpty(roleList)){\n            List<String> roles = roleList.stream().map(UmsRole::getName).collect(Collectors.toList());\n            data.put(\"roles\",roles);\n        }\n        return CommonResult.success(data);\n    }\n\n    @ApiOperation(value = \"登出功能\")\n    @RequestMapping(value = \"/logout\", method = RequestMethod.POST)\n    @ResponseBody\n    public CommonResult logout(Principal principal) {\n        adminService.logout(principal.getName());\n        return CommonResult.success(null);\n    }\n\n    @ApiOperation(\"根据用户名或姓名分页获取用户列表\")\n    @RequestMapping(value = \"/list\", method = RequestMethod.GET)\n    @ResponseBody\n    public CommonResult<CommonPage<UmsAdmin>> list(@RequestParam(value = \"keyword\", required = false) String keyword,\n                                                   @RequestParam(value = \"pageSize\", defaultValue = \"5\") Integer pageSize,\n                                                   @RequestParam(value = \"pageNum\", defaultValue = \"1\") Integer pageNum) {\n        List<UmsAdmin> adminList = adminService.list(keyword, pageSize, pageNum);\n        return CommonResult.success(CommonPage.restPage(adminList));\n    }\n\n    @ApiOperation(\"获取指定用户信息\")\n    @RequestMapping(value = \"/{id}\", method = RequestMethod.GET)\n    @ResponseBody\n    public CommonResult<UmsAdmin> getItem(@PathVariable Long id) {\n        UmsAdmin admin = adminService.getItem(id);\n        return CommonResult.success(admin);\n    }\n\n    @ApiOperation(\"修改指定用户信息\")\n    @RequestMapping(value = \"/update/{id}\", method = RequestMethod.POST)\n    @ResponseBody\n    public CommonResult update(@PathVariable Long id, @RequestBody UmsAdmin admin) {\n        int count = adminService.update(id, admin);\n        if (count > 0) {\n            return CommonResult.success(count);\n        }\n        return CommonResult.failed();\n    }\n\n    @ApiOperation(\"修改指定用户密码\")\n    @RequestMapping(value = \"/updatePassword\", method = RequestMethod.POST)\n    @ResponseBody\n    public CommonResult updatePassword(@Validated @RequestBody UpdateAdminPasswordParam updatePasswordParam) {\n        int status = adminService.updatePassword(updatePasswordParam);\n        if (status > 0) {\n            return CommonResult.success(status);\n        } else if (status == -1) {\n            return CommonResult.failed(\"提交参数不合法\");\n        } else if (status == -2) {\n            return CommonResult.failed(\"找不到该用户\");\n        } else if (status == -3) {\n            return CommonResult.failed(\"旧密码错误\");\n        } else {\n            return CommonResult.failed();\n        }\n    }\n\n    @ApiOperation(\"删除指定用户信息\")\n    @RequestMapping(value = \"/delete/{id}\", method = RequestMethod.POST)\n    @ResponseBody\n    public CommonResult delete(@PathVariable Long id) {\n        int count = adminService.delete(id);\n        if (count > 0) {\n            return CommonResult.success(count);\n        }\n        return CommonResult.failed();\n    }\n\n    @ApiOperation(\"修改帐号状态\")\n    @RequestMapping(value = \"/updateStatus/{id}\", method = RequestMethod.POST)\n    @ResponseBody\n    public CommonResult updateStatus(@PathVariable Long id,@RequestParam(value = \"status\") Integer status) {\n        UmsAdmin umsAdmin = new UmsAdmin();\n        umsAdmin.setStatus(status);\n        int count = adminService.update(id,umsAdmin);\n        if (count > 0) {\n            return CommonResult.success(count);\n        }\n        return CommonResult.failed();\n    }\n\n    @ApiOperation(\"给用户分配角色\")\n    @RequestMapping(value = \"/role/update\", method = RequestMethod.POST)\n    @ResponseBody\n    public CommonResult updateRole(@RequestParam(\"adminId\") Long adminId,\n                                   @RequestParam(\"roleIds\") List<Long> roleIds) {\n        int count = adminService.updateRole(adminId, roleIds);\n        if (count >= 0) {\n            return CommonResult.success(count);\n        }\n        return CommonResult.failed();\n    }\n\n    @ApiOperation(\"获取指定用户的角色\")\n    @RequestMapping(value = \"/role/{adminId}\", method = RequestMethod.GET)\n    @ResponseBody\n    public CommonResult<List<UmsRole>> getRoleList(@PathVariable Long adminId) {\n        List<UmsRole> roleList = adminService.getRoleList(adminId);\n        return CommonResult.success(roleList);\n    }\n\n}\n"}, {"id": 16, "content": "    @Override\n    public void register(String username, String password, String telephone, String authCode) {\n        //验证验证码\n        if(!verifyAuthCode(authCode,telephone)){\n            Asserts.fail(\"验证码错误\");\n        }\n        //查询是否已有该用户\n        UmsMemberExample example = new UmsMemberExample();\n        example.createCriteria().andUsernameEqualTo(username);\n        example.or(example.createCriteria().andPhoneEqualTo(telephone));\n        List<UmsMember> umsMembers = memberMapper.selectByExample(example);\n        if (!CollectionUtils.isEmpty(umsMembers)) {\n            Asserts.fail(\"该用户已经存在\");\n        }\n        //没有该用户进行添加操作\n        UmsMember umsMember = new UmsMember();\n        umsMember.setUsername(username);\n        umsMember.setPhone(telephone);\n        umsMember.setPassword(passwordEncoder.encode(password));\n        umsMember.setCreateTime(new Date());\n        umsMember.setStatus(1);\n        //获取默认会员等级并设置\n        UmsMemberLevelExample levelExample = new UmsMemberLevelExample();\n        levelExample.createCriteria().andDefaultStatusEqualTo(1);\n        List<UmsMemberLevel> memberLevelList = memberLevelMapper.selectByExample(levelExample);\n        if (!CollectionUtils.isEmpty(memberLevelList)) {\n            umsMember.setMemberLevelId(memberLevelList.get(0).getId());\n        }\n        memberMapper.insert(umsMember);\n        umsMember.setPassword(null);\n    }\n"}, {"id": 17, "content": "    @Bean\n    public UserDetailsService userDetailsService() {\n        //获取登录用户信息\n        return username -> adminService.loadUserByUsername(username);\n    }\n"}, {"id": 18, "content": "/**\n * mall-security模块相关配置\n * Created by macro on 2019/11/5.\n */\n@Configuration\npublic class MallSecurityConfig {\n\n    @Autowired\n    private UmsMemberService memberService;\n\n    @Bean\n    public UserDetailsService userDetailsService() {\n        //获取登录用户信息\n        return username -> memberService.loadUserByUsername(username);\n    }\n}\n"}, {"id": 19, "content": "    @Override\n    public UmsAdmin register(UmsAdminParam umsAdminParam) {\n        UmsAdmin umsAdmin = new UmsAdmin();\n        BeanUtils.copyProperties(umsAdminParam, umsAdmin);\n        umsAdmin.setCreateTime(new Date());\n        umsAdmin.setStatus(1);\n        //查询是否有相同用户名的用户\n        UmsAdminExample example = new UmsAdminExample();\n        example.createCriteria().andUsernameEqualTo(umsAdmin.getUsername());\n        List<UmsAdmin> umsAdminList = adminMapper.selectByExample(example);\n        if (umsAdminList.size() > 0) {\n            return null;\n        }\n        //将密码进行加密操作\n        String encodePassword = passwordEncoder.encode(umsAdmin.getPassword());\n        umsAdmin.setPassword(encodePassword);\n        adminMapper.insert(umsAdmin);\n        return umsAdmin;\n    }\n"}, {"id": 20, "content": "    public void setEmail(String email) {\n        this.email = email;\n    }\n"}, {"id": 21, "content": "    @Override\n    public UmsAdmin getAdminByUsername(String username) {\n        //先从缓存中获取数据\n        UmsAdmin admin = getCacheService().getAdmin(username);\n        if (admin != null) return admin;\n        //缓存中没有从数据库中获取\n        UmsAdminExample example = new UmsAdminExample();\n        example.createCriteria().andUsernameEqualTo(username);\n        List<UmsAdmin> adminList = adminMapper.selectByExample(example);\n        if (adminList != null && adminList.size() > 0) {\n            admin = adminList.get(0);\n            //将数据库中的数据存入缓存中\n            getCacheService().setAdmin(admin);\n            return admin;\n        }\n        return null;\n    }\n"}, {"id": 22, "content": "    @Override\n    public void logout(String username) {\n        //清空缓存中的用户相关数据\n        UmsAdmin admin = getCacheService().getAdmin(username);\n        getCacheService().delAdmin(admin.getId());\n        getCacheService().delResourceList(admin.getId());\n    }\n"}, {"id": 23, "content": "/**\n * 会员等级管理Service\n * Created by macro on 2018/4/26.\n */\npublic interface UmsMemberLevelService {\n    /**\n     * 获取所有会员等级\n     * @param defaultStatus 是否为默认会员\n     */\n    List<UmsMemberLevel> list(Integer defaultStatus);\n}\n"}, {"id": 24, "content": "/**\n * 会员管理Service实现类\n * Created by macro on 2018/8/3.\n */\n@Service\npublic class UmsMemberServiceImpl implements UmsMemberService {\n    private static final Logger LOGGER = LoggerFactory.getLogger(UmsMemberServiceImpl.class);\n    @Autowired\n    private PasswordEncoder passwordEncoder;\n    @Autowired\n    private JwtTokenUtil jwtTokenUtil;\n    @Autowired\n    private UmsMemberMapper memberMapper;\n    @Autowired\n    private UmsMemberLevelMapper memberLevelMapper;\n    @Autowired\n    private UmsMemberCacheService memberCacheService;\n    @Value(\"${redis.key.authCode}\")\n    private String REDIS_KEY_PREFIX_AUTH_CODE;\n    @Value(\"${redis.expire.authCode}\")\n    private Long AUTH_CODE_EXPIRE_SECONDS;\n\n    @Override\n    public UmsMember getByUsername(String username) {\n        UmsMember member = memberCacheService.getMember(username);\n        if(member!=null) return member;\n        UmsMemberExample example = new UmsMemberExample();\n        example.createCriteria().andUsernameEqualTo(username);\n        List<UmsMember> memberList = memberMapper.selectByExample(example);\n        if (!CollectionUtils.isEmpty(memberList)) {\n            member = memberList.get(0);\n            memberCacheService.setMember(member);\n            return member;\n        }\n        return null;\n    }\n\n    @Override\n    public UmsMember getById(Long id) {\n        return memberMapper.selectByPrimaryKey(id);\n    }\n\n    @Override\n    public void register(String username, String password, String telephone, String authCode) {\n        //验证验证码\n        if(!verifyAuthCode(authCode,telephone)){\n            Asserts.fail(\"验证码错误\");\n        }\n        //查询是否已有该用户\n        UmsMemberExample example = new UmsMemberExample();\n        example.createCriteria().andUsernameEqualTo(username);\n        example.or(example.createCriteria().andPhoneEqualTo(telephone));\n        List<UmsMember> umsMembers = memberMapper.selectByExample(example);\n        if (!CollectionUtils.isEmpty(umsMembers)) {\n            Asserts.fail(\"该用户已经存在\");\n        }\n        //没有该用户进行添加操作\n        UmsMember umsMember = new UmsMember();\n        umsMember.setUsername(username);\n        umsMember.setPhone(telephone);\n        umsMember.setPassword(passwordEncoder.encode(password));\n        umsMember.setCreateTime(new Date());\n        umsMember.setStatus(1);\n        //获取默认会员等级并设置\n        UmsMemberLevelExample levelExample = new UmsMemberLevelExample();\n        levelExample.createCriteria().andDefaultStatusEqualTo(1);\n        List<UmsMemberLevel> memberLevelList = memberLevelMapper.selectByExample(levelExample);\n        if (!CollectionUtils.isEmpty(memberLevelList)) {\n            umsMember.setMemberLevelId(memberLevelList.get(0).getId());\n        }\n        memberMapper.insert(umsMember);\n        umsMember.setPassword(null);\n    }\n\n    @Override\n    public String generateAuthCode(String telephone) {\n        StringBuilder sb = new StringBuilder();\n        Random random = new Random();\n        for(int i=0;i<6;i++){\n            sb.append(random.nextInt(10));\n        }\n        memberCacheService.setAuthCode(telephone,sb.toString());\n        return sb.toString();\n    }\n\n    @Override\n    public void updatePassword(String telephone, String password, String authCode) {\n        UmsMemberExample example = new UmsMemberExample();\n        example.createCriteria().andPhoneEqualTo(telephone);\n        List<UmsMember> memberList = memberMapper.selectByExample(example);\n        if(CollectionUtils.isEmpty(memberList)){\n            Asserts.fail(\"该账号不存在\");\n        }\n        //验证验证码\n        if(!verifyAuthCode(authCode,telephone)){\n            Asserts.fail(\"验证码错误\");\n        }\n        UmsMember umsMember = memberList.get(0);\n        umsMember.setPassword(passwordEncoder.encode(password));\n        memberMapper.updateByPrimaryKeySelective(umsMember);\n        memberCacheService.delMember(umsMember.getId());\n    }\n\n    @Override\n    public UmsMember getCurrentMember() {\n        SecurityContext ctx = SecurityContextHolder.getContext();\n        Authentication auth = ctx.getAuthentication();\n        MemberDetails memberDetails = (MemberDetails) auth.getPrincipal();\n        return memberDetails.getUmsMember();\n    }\n\n    @Override\n    public void updateIntegration(Long id, Integer integration) {\n        UmsMember record=new UmsMember();\n        record.setId(id);\n        record.setIntegration(integration);\n        memberMapper.updateByPrimaryKeySelective(record);\n        memberCacheService.delMember(id);\n    }\n\n    @Override\n    public UserDetails loadUserByUsername(String username) {\n        UmsMember member = getByUsername(username);\n        if(member!=null){\n            return new MemberDetails(member);\n        }\n        throw new UsernameNotFoundException(\"用户名或密码错误\");\n    }\n\n    @Override\n    public String login(String username, String password) {\n        String token = null;\n        //密码需要客户端加密后传递\n        try {\n            UserDetails userDetails = loadUserByUsername(username);\n            if(!passwordEncoder.matches(password,userDetails.getPassword())){\n                throw new BadCredentialsException(\"密码不正确\");\n            }\n            UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(userDetails, null, userDetails.getAuthorities());\n            SecurityContextHolder.getContext().setAuthentication(authentication);\n            token = jwtTokenUtil.generateToken(userDetails);\n        } catch (AuthenticationException e) {\n            LOGGER.warn(\"登录异常:{}\", e.getMessage());\n        }\n        return token;\n    }\n\n    @Override\n    public String refreshToken(String token) {\n        return jwtTokenUtil.refreshHeadToken(token);\n    }\n\n    //对输入的验证码进行校验\n    private boolean verifyAuthCode(String authCode, String telephone){\n        if(StrUtil.isEmpty(authCode)){\n            return false;\n        }\n        String realAuthCode = memberCacheService.getAuthCode(telephone);\n        return authCode.equals(realAuthCode);\n    }\n\n}\n"}, {"id": 25, "content": "/**\n * 后台用户与角色关系管理自定义Dao\n * Created by macro on 2018/10/8.\n */\npublic interface UmsAdminRoleRelationDao {\n    /**\n     * 批量插入用户角色关系\n     */\n    int insertList(@Param(\"list\") List<UmsAdminRoleRelation> adminRoleRelationList);\n\n    /**\n     * 获取用于所有角色\n     */\n    List<UmsRole> getRoleList(@Param(\"adminId\") Long adminId);\n\n    /**\n     * 获取用户所有可访问资源\n     */\n    List<UmsResource> getResourceList(@Param(\"adminId\") Long adminId);\n\n    /**\n     * 获取资源相关用户ID列表\n     */\n    List<Long> getAdminIdList(@Param(\"resourceId\") Long resourceId);\n}\n"}, {"id": 26, "content": "    public String getUsername() {\n        return username;\n    }\n"}, {"id": 27, "content": "    public String getUsername() {\n        return username;\n    }\n"}, {"id": 28, "content": "    public String getPassword() {\n        return password;\n    }\n"}, {"id": 29, "content": "    public String getPassword() {\n        return password;\n    }\n"}, {"id": 30, "content": "    @Override\n    public UmsAdmin getAdmin(String username) {\n        String key = REDIS_DATABASE + \":\" + REDIS_KEY_ADMIN + \":\" + username;\n        return (UmsAdmin) redisService.get(key);\n    }\n"}, {"id": 31, "content": "/**\n * 会员信息缓存业务类\n * Created by macro on 2020/3/14.\n */\npublic interface UmsMemberCacheService {\n    /**\n     * 删除会员用户缓存\n     */\n    void delMember(Long memberId);\n\n    /**\n     * 获取会员用户缓存\n     */\n    UmsMember getMember(String username);\n\n    /**\n     * 设置会员用户缓存\n     */\n    void setMember(UmsMember member);\n\n    /**\n     * 设置验证码\n     */\n    void setAuthCode(String telephone, String authCode);\n\n    /**\n     * 获取验证码\n     */\n    String getAuthCode(String telephone);\n}\n"}, {"id": 32, "content": "    @Override\n    public String login(String username, String password) {\n        String token = null;\n        //密码需要客户端加密后传递\n        try {\n            UserDetails userDetails = loadUserByUsername(username);\n            if(!passwordEncoder.matches(password,userDetails.getPassword())){\n                Asserts.fail(\"密码不正确\");\n            }\n            if(!userDetails.isEnabled()){\n                Asserts.fail(\"帐号已被禁用\");\n            }\n            UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(userDetails, null, userDetails.getAuthorities());\n            SecurityContextHolder.getContext().setAuthentication(authentication);\n            token = jwtTokenUtil.generateToken(userDetails);\n//            updateLoginTimeByUsername(username);\n            insertLoginLog(username);\n        } catch (AuthenticationException e) {\n            LOGGER.warn(\"登录异常:{}\", e.getMessage());\n        }\n        return token;\n    }\n"}, {"id": 33, "content": "    @ApiOperation(\"获取指定用户信息\")\n    @RequestMapping(value = \"/{id}\", method = RequestMethod.GET)\n    @ResponseBody\n    public CommonResult<UmsAdmin> getItem(@PathVariable Long id) {\n        UmsAdmin admin = adminService.getItem(id);\n        return CommonResult.success(admin);\n    }\n"}, {"id": 34, "content": "    @ApiOperation(value = \"获取当前登录用户信息\")\n    @RequestMapping(value = \"/info\", method = RequestMethod.GET)\n    @ResponseBody\n    public CommonResult getAdminInfo(Principal principal) {\n        if(principal==null){\n            return CommonResult.unauthorized(null);\n        }\n        String username = principal.getName();\n        UmsAdmin umsAdmin = adminService.getAdminByUsername(username);\n        Map<String, Object> data = new HashMap<>();\n        data.put(\"username\", umsAdmin.getUsername());\n        data.put(\"menus\", roleService.getMenuList(umsAdmin.getId()));\n        data.put(\"icon\", umsAdmin.getIcon());\n        List<UmsRole> roleList = adminService.getRoleList(umsAdmin.getId());\n        if(CollUtil.isNotEmpty(roleList)){\n            List<String> roles = roleList.stream().map(UmsRole::getName).collect(Collectors.toList());\n            data.put(\"roles\",roles);\n        }\n        return CommonResult.success(data);\n    }\n"}, {"id": 35, "content": "    /**\n     * 根据用户名修改登录时间\n     */\n    private void updateLoginTimeByUsername(String username) {\n        UmsAdmin record = new UmsAdmin();\n        record.setLoginTime(new Date());\n        UmsAdminExample example = new UmsAdminExample();\n        example.createCriteria().andUsernameEqualTo(username);\n        adminMapper.updateByExampleSelective(record, example);\n    }\n"}, {"id": 36, "content": "    @ApiOperation(\"会员注册\")\n    @RequestMapping(value = \"/register\", method = RequestMethod.POST)\n    @ResponseBody\n    public CommonResult register(@RequestParam String username,\n                                 @RequestParam String password,\n                                 @RequestParam String telephone,\n                                 @RequestParam String authCode) {\n        memberService.register(username, password, telephone, authCode);\n        return CommonResult.success(null,\"注册成功\");\n    }\n"}, {"id": 37, "content": "/**\n * SpringSecurity相关配置\n * Created by macro on 2018/4/26.\n */\n@Configuration\n@EnableWebSecurity\npublic class SecurityConfig extends WebSecurityConfigurerAdapter {\n    @Autowired\n    private UmsAdminMapper umsAdminMapper;\n\n    @Override\n    protected void configure(HttpSecurity http) throws Exception {\n        http.authorizeRequests()//配置权限\n//                .antMatchers(\"/\").access(\"hasRole('TEST')\")//该路径需要TEST角色\n//                .antMatchers(\"/brand/list\").hasAuthority(\"TEST\")//该路径需要TEST权限\n                .antMatchers(\"/**\").permitAll()\n                .and()//启用基于http的认证\n                .httpBasic()\n                .realmName(\"/\")\n                .and()//配置登录页面\n                .formLogin()\n                .loginPage(\"/login\")\n                .failureUrl(\"/login?error=true\")\n                .and()//配置退出路径\n                .logout()\n                .logoutSuccessUrl(\"/\")\n//                .and()//记住密码功能\n//                .rememberMe()\n//                .tokenValiditySeconds(60*60*24)\n//                .key(\"rememberMeKey\")\n                .and()//关闭跨域伪造\n                .csrf()\n                .disable()\n                .headers()//去除X-Frame-Options\n                .frameOptions()\n                .disable();\n    }\n\n    @Override\n    protected void configure(AuthenticationManagerBuilder auth) throws Exception {\n        auth.userDetailsService(userDetailsService()).passwordEncoder(new BCryptPasswordEncoder());\n    }\n\n    @Bean\n    public UserDetailsService userDetailsService() {\n        //获取登录用户信息\n        return new UserDetailsService() {\n            @Override\n            public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {\n                UmsAdminExample example = new UmsAdminExample();\n                example.createCriteria().andUsernameEqualTo(username);\n                List<UmsAdmin> umsAdminList = umsAdminMapper.selectByExample(example);\n                if (umsAdminList != null && umsAdminList.size() > 0) {\n                    return new AdminUserDetails(umsAdminList.get(0));\n                }\n                throw new UsernameNotFoundException(\"用户名或密码错误\");\n            }\n        };\n    }\n}\n"}, {"id": 38, "content": "- 成员管理：添加、编辑、删除成员、成员列表、为成员分配角色、获取成员角色、分配+-权限、获取权限列表\n\n角色 | 菜单 \n----|----\n管理员 | 所有菜单权限\n运营 | 首页、用户、促销、运营、内容\n财务 | 首页、统计、财务\n美工 | 首页、商品\n客服 | 首页、商品、订单\n\n## 前台功能\n\n### 商品搜索\n\n> **综合搜索功能**\n\n- 搜索：根据商品标题、副标题、关键字进行搜索；\n- 筛选：未选择分类时聚合搜索结果，选择出现次数最多的分类，选择分类以后可以根据选择分类的筛选字段进行筛选；\n- 排序：按新品、销量、价格进行排序\n- 搜索返回结果：商品ID、商品图片、名称、副标题、价格、商品销量、新品、商品的参数、品牌名称、分类名称\n- 接口：从数据库中查询相关数据并导入es,插入（修改）数据接口，删除数据接口\n- 品牌分类筛选：根据搜索结果聚合返回品牌、分类及属性\n"}, {"id": 39, "content": "    @Override\n    public UmsMember getByUsername(String username) {\n        UmsMember member = memberCacheService.getMember(username);\n        if(member!=null) return member;\n        UmsMemberExample example = new UmsMemberExample();\n        example.createCriteria().andUsernameEqualTo(username);\n        List<UmsMember> memberList = memberMapper.selectByExample(example);\n        if (!CollectionUtils.isEmpty(memberList)) {\n            member = memberList.get(0);\n            memberCacheService.setMember(member);\n            return member;\n        }\n        return null;\n    }\n"}, {"id": 40, "content": "/**\n * 后台用户缓存管理Service\n * Created by macro on 2020/3/13.\n */\npublic interface UmsAdminCacheService {\n    /**\n     * 删除后台用户缓存\n     */\n    void delAdmin(Long adminId);\n\n    /**\n     * 删除后台用户资源列表缓存\n     */\n    void delResourceList(Long adminId);\n\n    /**\n     * 当角色相关资源信息改变时删除相关后台用户缓存\n     */\n    void delResourceListByRole(Long roleId);\n\n    /**\n     * 当角色相关资源信息改变时删除相关后台用户缓存\n     */\n    void delResourceListByRoleIds(List<Long> roleIds);\n\n    /**\n     * 当资源信息改变时，删除资源项目后台用户缓存\n     */\n    void delResourceListByResource(Long resourceId);\n\n    /**\n     * 获取缓存后台用户信息\n     */\n    UmsAdmin getAdmin(String username);\n\n    /**\n     * 设置缓存后台用户信息\n     */\n    void setAdmin(UmsAdmin admin);\n\n    /**\n     * 获取缓存后台用户资源列表\n     */\n    List<UmsResource> getResourceList(Long adminId);\n\n    /**\n     * 设置缓存后台用户资源列表\n     */\n    void setResourceList(Long adminId, List<UmsResource> resourceList);\n}\n"}, {"id": 41, "content": "    @Override\n    public boolean isEnabled() {\n        return umsAdmin.getStatus().equals(1);\n    }\n"}, {"id": 42, "content": "    @Override\n    public List<UmsAdmin> list(String keyword, Integer pageSize, Integer pageNum) {\n        PageHelper.startPage(pageNum, pageSize);\n        UmsAdminExample example = new UmsAdminExample();\n        UmsAdminExample.Criteria criteria = example.createCriteria();\n        if (!StrUtil.isEmpty(keyword)) {\n            criteria.andUsernameLike(\"%\" + keyword + \"%\");\n            example.or(example.createCriteria().andNickNameLike(\"%\" + keyword + \"%\"));\n        }\n        return adminMapper.selectByExample(example);\n    }\n"}, {"id": 43, "content": "    public void setAdminCount(Integer adminCount) {\n        this.adminCount = adminCount;\n    }\n"}, {"id": 44, "content": "public class UmsAdmin implements Serializable {\n    private Long id;\n\n    private String username;\n\n    private String password;\n\n    @ApiModelProperty(value = \"头像\")\n    private String icon;\n\n    @ApiModelProperty(value = \"邮箱\")\n    private String email;\n\n    @ApiModelProperty(value = \"昵称\")\n    private String nickName;\n\n    @ApiModelProperty(value = \"备注信息\")\n    private String note;\n\n    @ApiModelProperty(value = \"创建时间\")\n    private Date createTime;\n\n    @ApiModelProperty(value = \"最后登录时间\")\n    private Date loginTime;\n\n    @ApiModelProperty(value = \"帐号启用状态：0->禁用；1->启用\")\n    private Integer status;\n\n    private static final long serialVersionUID = 1L;\n\n    public Long getId() {\n        return id;\n    }\n\n    public void setId(Long id) {\n        this.id = id;\n    }\n\n    public String getUsername() {\n        return username;\n    }\n\n    public void setUsername(String username) {\n        this.username = username;\n    }\n\n    public String getPassword() {\n        return password;\n    }\n\n    public void setPassword(String password) {\n        this.password = password;\n    }\n\n    public String getIcon() {\n        return icon;\n    }\n\n    public void setIcon(String icon) {\n        this.icon = icon;\n    }\n\n    public String getEmail() {\n        return email;\n    }\n\n    public void setEmail(String email) {\n        this.email = email;\n    }\n\n    public String getNickName() {\n        return nickName;\n    }\n\n    public void setNickName(String nickName) {\n        this.nickName = nickName;\n    }\n\n    public String getNote() {\n        return note;\n    }\n\n    public void setNote(String note) {\n        this.note = note;\n    }\n\n    public Date getCreateTime() {\n        return createTime;\n    }\n\n    public void setCreateTime(Date createTime) {\n        this.createTime = createTime;\n    }\n\n    public Date getLoginTime() {\n        return loginTime;\n    }\n\n    public void setLoginTime(Date loginTime) {\n        this.loginTime = loginTime;\n    }\n\n    public Integer getStatus() {\n        return status;\n    }\n\n    public void setStatus(Integer status) {\n        this.status = status;\n    }\n\n    @Override\n    public String toString() {\n        StringBuilder sb = new StringBuilder();\n        sb.append(getClass().getSimpleName());\n        sb.append(\" [\");\n        sb.append(\"Hash = \").append(hashCode());\n        sb.append(\", id=\").append(id);\n        sb.append(\", username=\").append(username);\n        sb.append(\", password=\").append(password);\n        sb.append(\", icon=\").append(icon);\n        sb.append(\", email=\").append(email);\n        sb.append(\", nickName=\").append(nickName);\n        sb.append(\", note=\").append(note);\n        sb.append(\", createTime=\").append(createTime);\n        sb.append(\", loginTime=\").append(loginTime);\n        sb.append(\", status=\").append(status);\n        sb.append(\", serialVersionUID=\").append(serialVersionUID);\n        sb.append(\"]\");\n        return sb.toString();\n    }\n}"}, {"id": 45, "content": "/**\n * 会员管理Controller\n * Created by macro on 2018/8/3.\n */\n@Controller\n@Api(tags = \"UmsMemberController\")\n@Tag(name = \"UmsMemberController\", description = \"会员登录注册管理\")\n@RequestMapping(\"/sso\")\npublic class UmsMemberController {\n    @Value(\"${jwt.tokenHeader}\")\n    private String tokenHeader;\n    @Value(\"${jwt.tokenHead}\")\n    private String tokenHead;\n    @Autowired\n    private UmsMemberService memberService;\n\n    @ApiOperation(\"会员注册\")\n    @RequestMapping(value = \"/register\", method = RequestMethod.POST)\n    @ResponseBody\n    public CommonResult register(@RequestParam String username,\n                                 @RequestParam String password,\n                                 @RequestParam String telephone,\n                                 @RequestParam String authCode) {\n        memberService.register(username, password, telephone, authCode);\n        return CommonResult.success(null,\"注册成功\");\n    }\n\n    @ApiOperation(\"会员登录\")\n    @RequestMapping(value = \"/login\", method = RequestMethod.POST)\n    @ResponseBody\n    public CommonResult login(@RequestParam String username,\n                              @RequestParam String password) {\n        String token = memberService.login(username, password);\n        if (token == null) {\n            return CommonResult.validateFailed(\"用户名或密码错误\");\n        }\n        Map<String, String> tokenMap = new HashMap<>();\n        tokenMap.put(\"token\", token);\n        tokenMap.put(\"tokenHead\", tokenHead);\n        return CommonResult.success(tokenMap);\n    }\n\n    @ApiOperation(\"获取会员信息\")\n    @RequestMapping(value = \"/info\", method = RequestMethod.GET)\n    @ResponseBody\n    public CommonResult info(Principal principal) {\n        if(principal==null){\n            return CommonResult.unauthorized(null);\n        }\n        UmsMember member = memberService.getCurrentMember();\n        return CommonResult.success(member);\n    }\n\n    @ApiOperation(\"获取验证码\")\n    @RequestMapping(value = \"/getAuthCode\", method = RequestMethod.GET)\n    @ResponseBody\n    public CommonResult getAuthCode(@RequestParam String telephone) {\n        String authCode = memberService.generateAuthCode(telephone);\n        return CommonResult.success(authCode,\"获取验证码成功\");\n    }\n\n    @ApiOperation(\"会员修改密码\")\n    @RequestMapping(value = \"/updatePassword\", method = RequestMethod.POST)\n    @ResponseBody\n    public CommonResult updatePassword(@RequestParam String telephone,\n                                 @RequestParam String password,\n                                 @RequestParam String authCode) {\n        memberService.updatePassword(telephone,password,authCode);\n        return CommonResult.success(null,\"密码修改成功\");\n    }\n\n\n    @ApiOperation(value = \"刷新token\")\n    @RequestMapping(value = \"/refreshToken\", method = RequestMethod.GET)\n    @ResponseBody\n    public CommonResult refreshToken(HttpServletRequest request) {\n        String token = request.getHeader(tokenHeader);\n        String refreshToken = memberService.refreshToken(token);\n        if (refreshToken == null) {\n            return CommonResult.failed(\"token已经过期！\");\n        }\n        Map<String, String> tokenMap = new HashMap<>();\n        tokenMap.put(\"token\", refreshToken);\n        tokenMap.put(\"tokenHead\", tokenHead);\n        return CommonResult.success(tokenMap);\n    }\n}\n"}, {"id": 46, "content": "    @Override\n    public String login(String username, String password) {\n        String token = null;\n        //密码需要客户端加密后传递\n        try {\n            UserDetails userDetails = loadUserByUsername(username);\n            if(!passwordEncoder.matches(password,userDetails.getPassword())){\n                throw new BadCredentialsException(\"密码不正确\");\n            }\n            UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(userDetails, null, userDetails.getAuthorities());\n            SecurityContextHolder.getContext().setAuthentication(authentication);\n            token = jwtTokenUtil.generateToken(userDetails);\n        } catch (AuthenticationException e) {\n            LOGGER.warn(\"登录异常:{}\", e.getMessage());\n        }\n        return token;\n    }\n"}, {"id": 47, "content": "/**\n * 会员浏览记录管理Service\n * Created by macro on 2018/8/3.\n */\npublic interface MemberReadHistoryService {\n    /**\n     * 生成浏览记录\n     */\n    int create(MemberReadHistory memberReadHistory);\n\n    /**\n     * 批量删除浏览记录\n     */\n    int delete(List<String> ids);\n\n    /**\n     * 分页获取用户浏览历史记录\n     */\n    Page<MemberReadHistory> list(Integer pageNum, Integer pageSize);\n\n    /**\n     * 清空浏览记录\n     */\n    void clear();\n}\n"}, {"id": 48, "content": "/**\n * 后台角色管理自定义Dao\n * Created by macro on 2020/2/2.\n */\npublic interface UmsRoleDao {\n    /**\n     * 根据后台用户ID获取菜单\n     */\n    List<UmsMenu> getMenuList(@Param(\"adminId\") Long adminId);\n    /**\n     * 根据角色ID获取菜单\n     */\n    List<UmsMenu> getMenuListByRoleId(@Param(\"roleId\") Long roleId);\n    /**\n     * 根据角色ID获取资源\n     */\n    List<UmsResource> getResourceListByRoleId(@Param(\"roleId\") Long roleId);\n}\n"}, {"id": 49, "content": "/**\n * 后台角色管理Service实现类\n * Created by macro on 2018/9/30.\n */\n@Service\npublic class UmsRoleServiceImpl implements UmsRoleService {\n    @Autowired\n    private UmsRoleMapper roleMapper;\n    @Autowired\n    private UmsRoleMenuRelationMapper roleMenuRelationMapper;\n    @Autowired\n    private UmsRoleResourceRelationMapper roleResourceRelationMapper;\n    @Autowired\n    private UmsRoleDao roleDao;\n    @Autowired\n    private UmsAdminCacheService adminCacheService;\n    @Override\n    public int create(UmsRole role) {\n        role.setCreateTime(new Date());\n        role.setAdminCount(0);\n        role.setSort(0);\n        return roleMapper.insert(role);\n    }\n\n    @Override\n    public int update(Long id, UmsRole role) {\n        role.setId(id);\n        return roleMapper.updateByPrimaryKeySelective(role);\n    }\n\n    @Override\n    public int delete(List<Long> ids) {\n        UmsRoleExample example = new UmsRoleExample();\n        example.createCriteria().andIdIn(ids);\n        int count = roleMapper.deleteByExample(example);\n        adminCacheService.delResourceListByRoleIds(ids);\n        return count;\n    }\n\n    @Override\n    public List<UmsRole> list() {\n        return roleMapper.selectByExample(new UmsRoleExample());\n    }\n\n    @Override\n    public List<UmsRole> list(String keyword, Integer pageSize, Integer pageNum) {\n        PageHelper.startPage(pageNum, pageSize);\n        UmsRoleExample example = new UmsRoleExample();\n        if (!StrUtil.isEmpty(keyword)) {\n            example.createCriteria().andNameLike(\"%\" + keyword + \"%\");\n        }\n        return roleMapper.selectByExample(example);\n    }\n\n    @Override\n    public List<UmsMenu> getMenuList(Long adminId) {\n        return roleDao.getMenuList(adminId);\n    }\n\n    @Override\n    public List<UmsMenu> listMenu(Long roleId) {\n        return roleDao.getMenuListByRoleId(roleId);\n    }\n\n    @Override\n    public List<UmsResource> listResource(Long roleId) {\n        return roleDao.getResourceListByRoleId(roleId);\n    }\n\n    @Override\n    public int allocMenu(Long roleId, List<Long> menuIds) {\n        //先删除原有关系\n        UmsRoleMenuRelationExample example=new UmsRoleMenuRelationExample();\n        example.createCriteria().andRoleIdEqualTo(roleId);\n        roleMenuRelationMapper.deleteByExample(example);\n        //批量插入新关系\n        for (Long menuId : menuIds) {\n            UmsRoleMenuRelation relation = new UmsRoleMenuRelation();\n            relation.setRoleId(roleId);\n            relation.setMenuId(menuId);\n            roleMenuRelationMapper.insert(relation);\n        }\n        return menuIds.size();\n    }\n\n    @Override\n    public int allocResource(Long roleId, List<Long> resourceIds) {\n        //先删除原有关系\n        UmsRoleResourceRelationExample example=new UmsRoleResourceRelationExample();\n        example.createCriteria().andRoleIdEqualTo(roleId);\n        roleResourceRelationMapper.deleteByExample(example);\n        //批量插入新关系\n        for (Long resourceId : resourceIds) {\n            UmsRoleResourceRelation relation = new UmsRoleResourceRelation();\n            relation.setRoleId(roleId);\n            relation.setResourceId(resourceId);\n            roleResourceRelationMapper.insert(relation);\n        }\n        adminCacheService.delResourceListByRole(roleId);\n        return resourceIds.size();\n    }\n}\n"}, {"id": 50, "content": "    @ApiOperation(\"获取会员信息\")\n    @RequestMapping(value = \"/info\", method = RequestMethod.GET)\n    @ResponseBody\n    public CommonResult info(Principal principal) {\n        if(principal==null){\n            return CommonResult.unauthorized(null);\n        }\n        UmsMember member = memberService.getCurrentMember();\n        return CommonResult.success(member);\n    }\n"}, {"id": 51, "content": "    public void setLoginCount(Integer loginCount) {\n        this.loginCount = loginCount;\n    }\n"}, {"id": 52, "content": "    public void setUsername(String username) {\n        this.username = username;\n    }\n"}, {"id": 53, "content": "    public void setUsername(String username) {\n        this.username = username;\n    }\n"}, {"id": 54, "content": "/**\n * 后台角色管理Service\n * Created by macro on 2018/9/30.\n */\npublic interface UmsRoleService {\n    /**\n     * 添加角色\n     */\n    int create(UmsRole role);\n\n    /**\n     * 修改角色信息\n     */\n    int update(Long id, UmsRole role);\n\n    /**\n     * 批量删除角色\n     */\n    int delete(List<Long> ids);\n\n    /**\n     * 获取所有角色列表\n     */\n    List<UmsRole> list();\n\n    /**\n     * 分页获取角色列表\n     */\n    List<UmsRole> list(String keyword, Integer pageSize, Integer pageNum);\n\n    /**\n     * 根据管理员ID获取对应菜单\n     */\n    List<UmsMenu> getMenuList(Long adminId);\n\n    /**\n     * 获取角色相关菜单\n     */\n    List<UmsMenu> listMenu(Long roleId);\n\n    /**\n     * 获取角色相关资源\n     */\n    List<UmsResource> listResource(Long roleId);\n\n    /**\n     * 给角色分配菜单\n     */\n    @Transactional\n    int allocMenu(Long roleId, List<Long> menuIds);\n\n    /**\n     * 给角色分配资源\n     */\n    @Transactional\n    int allocResource(Long roleId, List<Long> resourceIds);\n}\n"}, {"id": 55, "content": "    @ApiOperation(\"根据用户名或姓名分页获取用户列表\")\n    @RequestMapping(value = \"/list\", method = RequestMethod.GET)\n    @ResponseBody\n    public CommonResult<CommonPage<UmsAdmin>> list(@RequestParam(value = \"keyword\", required = false) String keyword,\n                                                   @RequestParam(value = \"pageSize\", defaultValue = \"5\") Integer pageSize,\n                                                   @RequestParam(value = \"pageNum\", defaultValue = \"1\") Integer pageNum) {\n        List<UmsAdmin> adminList = adminService.list(keyword, pageSize, pageNum);\n        return CommonResult.success(CommonPage.restPage(adminList));\n    }\n"}, {"id": 56, "content": "/**\n * 后台用户角色管理Controller\n * Created by macro on 2018/9/30.\n */\n@Controller\n@Api(tags = \"UmsRoleController\")\n@Tag(name = \"UmsRoleController\", description = \"后台用户角色管理\")\n@RequestMapping(\"/role\")\npublic class UmsRoleController {\n    @Autowired\n    private UmsRoleService roleService;\n\n    @ApiOperation(\"添加角色\")\n    @RequestMapping(value = \"/create\", method = RequestMethod.POST)\n    @ResponseBody\n    public CommonResult create(@RequestBody UmsRole role) {\n        int count = roleService.create(role);\n        if (count > 0) {\n            return CommonResult.success(count);\n        }\n        return CommonResult.failed();\n    }\n\n    @ApiOperation(\"修改角色\")\n    @RequestMapping(value = \"/update/{id}\", method = RequestMethod.POST)\n    @ResponseBody\n    public CommonResult update(@PathVariable Long id, @RequestBody UmsRole role) {\n        int count = roleService.update(id, role);\n        if (count > 0) {\n            return CommonResult.success(count);\n        }\n        return CommonResult.failed();\n    }\n\n    @ApiOperation(\"批量删除角色\")\n    @RequestMapping(value = \"/delete\", method = RequestMethod.POST)\n    @ResponseBody\n    public CommonResult delete(@RequestParam(\"ids\") List<Long> ids) {\n        int count = roleService.delete(ids);\n        if (count > 0) {\n            return CommonResult.success(count);\n        }\n        return CommonResult.failed();\n    }\n\n    @ApiOperation(\"获取所有角色\")\n    @RequestMapping(value = \"/listAll\", method = RequestMethod.GET)\n    @ResponseBody\n    public CommonResult<List<UmsRole>> listAll() {\n        List<UmsRole> roleList = roleService.list();\n        return CommonResult.success(roleList);\n    }\n\n    @ApiOperation(\"根据角色名称分页获取角色列表\")\n    @RequestMapping(value = \"/list\", method = RequestMethod.GET)\n    @ResponseBody\n    public CommonResult<CommonPage<UmsRole>> list(@RequestParam(value = \"keyword\", required = false) String keyword,\n                                                  @RequestParam(value = \"pageSize\", defaultValue = \"5\") Integer pageSize,\n                                                  @RequestParam(value = \"pageNum\", defaultValue = \"1\") Integer pageNum) {\n        List<UmsRole> roleList = roleService.list(keyword, pageSize, pageNum);\n        return CommonResult.success(CommonPage.restPage(roleList));\n    }\n\n    @ApiOperation(\"修改角色状态\")\n    @RequestMapping(value = \"/updateStatus/{id}\", method = RequestMethod.POST)\n    @ResponseBody\n    public CommonResult updateStatus(@PathVariable Long id, @RequestParam(value = \"status\") Integer status) {\n        UmsRole umsRole = new UmsRole();\n        umsRole.setStatus(status);\n        int count = roleService.update(id, umsRole);\n        if (count > 0) {\n            return CommonResult.success(count);\n        }\n        return CommonResult.failed();\n    }\n\n    @ApiOperation(\"获取角色相关菜单\")\n    @RequestMapping(value = \"/listMenu/{roleId}\", method = RequestMethod.GET)\n    @ResponseBody\n    public CommonResult<List<UmsMenu>> listMenu(@PathVariable Long roleId) {\n        List<UmsMenu> roleList = roleService.listMenu(roleId);\n        return CommonResult.success(roleList);\n    }\n\n    @ApiOperation(\"获取角色相关资源\")\n    @RequestMapping(value = \"/listResource/{roleId}\", method = RequestMethod.GET)\n    @ResponseBody\n    public CommonResult<List<UmsResource>> listResource(@PathVariable Long roleId) {\n        List<UmsResource> roleList = roleService.listResource(roleId);\n        return CommonResult.success(roleList);\n    }\n\n    @ApiOperation(\"给角色分配菜单\")\n    @RequestMapping(value = \"/allocMenu\", method = RequestMethod.POST)\n    @ResponseBody\n    public CommonResult allocMenu(@RequestParam Long roleId, @RequestParam List<Long> menuIds) {\n        int count = roleService.allocMenu(roleId, menuIds);\n        return CommonResult.success(count);\n    }\n\n    @ApiOperation(\"给角色分配资源\")\n    @RequestMapping(value = \"/allocResource\", method = RequestMethod.POST)\n    @ResponseBody\n    public CommonResult allocResource(@RequestParam Long roleId, @RequestParam List<Long> resourceIds) {\n        int count = roleService.allocResource(roleId, resourceIds);\n        return CommonResult.success(count);\n    }\n\n}\n"}, {"id": 57, "content": "    public void setNickname(String nickname) {\n        this.nickname = nickname;\n    }\n"}, {"id": 58, "content": "    public void setAdminId(Long adminId) {\n        this.adminId = adminId;\n    }\n"}, {"id": 59, "content": "    public void setAdminId(Long adminId) {\n        this.adminId = adminId;\n    }\n"}, {"id": 60, "content": "    public void setAdminId(Long adminId) {\n        this.adminId = adminId;\n    }\n"}, {"id": 61, "content": "    public String getNickname() {\n        return nickname;\n    }\n"}, {"id": 62, "content": "    @Override\n    public boolean isEnabled() {\n        return true;\n    }\n"}, {"id": 63, "content": "    @ApiOperation(value = \"用户注册\")\n    @RequestMapping(value = \"/register\", method = RequestMethod.POST)\n    @ResponseBody\n    public CommonResult<UmsAdmin> register(@Validated @RequestBody UmsAdminParam umsAdminParam) {\n        UmsAdmin umsAdmin = adminService.register(umsAdminParam);\n        if (umsAdmin == null) {\n            return CommonResult.failed();\n        }\n        return CommonResult.success(umsAdmin);\n    }\n"}, {"id": 64, "content": "    @Override\n    public String getPassword() {\n        return umsAdmin.getPassword();\n    }\n"}, {"id": 65, "content": "    @Override\n    public String getPassword() {\n        return umsAdmin.getPassword();\n    }\n"}, {"id": 66, "content": "    @ApiOperation(\"会员登录\")\n    @RequestMapping(value = \"/login\", method = RequestMethod.POST)\n    @ResponseBody\n    public CommonResult login(@RequestParam String username,\n                              @RequestParam String password) {\n        String token = memberService.login(username, password);\n        if (token == null) {\n            return CommonResult.validateFailed(\"用户名或密码错误\");\n        }\n        Map<String, String> tokenMap = new HashMap<>();\n        tokenMap.put(\"token\", token);\n        tokenMap.put(\"tokenHead\", tokenHead);\n        return CommonResult.success(tokenMap);\n    }\n"}, {"id": 67, "content": "    @Override\n    public Collection<? extends GrantedAuthority> getAuthorities() {\n        //返回当前用户所拥有的资源\n        return resourceList.stream()\n                .map(resource ->new SimpleGrantedAuthority(resource.getId()+\":\"+resource.getName()))\n                .collect(Collectors.toList());\n    }\n"}, {"id": 68, "content": "- 优选列表：控制显示、排序、删除\n- 添加、编辑优选：关联和删除商品\n\n> **话题管理**\n\n- 专题列表：查看、删除、热门话题\n- 话题分类管理：控制显示、排序、编辑删除分类\n\n> **帮助管理**\n\n- 帮助列表：查看、删除、控制显示\n- 添加、编辑帮助：选择帮助分类\n- 帮助分类管理：控制显示、排序、编辑删除分类\n\n### 用户管理\n\n> **用户管理**\n\n- 用户列表：帐号启用、删除、群发短信\n- 批量操作：群发短信、站内信、推送、设置标签、赠送优惠券\n- 查看、编辑用户信息：用户详情（统计信息、收货地址、订单记录）、编辑资料、登录日志\n- 购买力筛选：最近消费、消费次数、消费金额、订单均价、商品分类、会员等级、用户标签\n- 用户标签管理：标签列表、添加、编辑、删除\n- 会员等级设置：列表、添加、编辑、设置默认会员等级\n\n> **成才值及积分**\n\n- 成长值及积分查询：列表展示、积分明细、成长值明细、修改数值\n"}, {"id": 69, "content": "    @Override\n    public UmsMember getMember(String username) {\n        String key = REDIS_DATABASE + \":\" + REDIS_KEY_MEMBER + \":\" + username;\n        return (UmsMember) redisService.get(key);\n    }\n"}, {"id": 70, "content": "/**\n * mall-security模块相关配置\n * Created by macro on 2019/11/9.\n */\n@Configuration\npublic class MallSecurityConfig {\n\n    @Autowired\n    private UmsAdminService adminService;\n    @Autowired\n    private UmsResourceService resourceService;\n\n    @Bean\n    public UserDetailsService userDetailsService() {\n        //获取登录用户信息\n        return username -> adminService.loadUserByUsername(username);\n    }\n\n    @Bean\n    public DynamicSecurityService dynamicSecurityService() {\n        return new DynamicSecurityService() {\n            @Override\n            public Map<String, ConfigAttribute> loadDataSource() {\n                Map<String, ConfigAttribute> map = new ConcurrentHashMap<>();\n                List<UmsResource> resourceList = resourceService.listAll();\n                for (UmsResource resource : resourceList) {\n                    map.put(resource.getUrl(), new org.springframework.security.access.SecurityConfig(resource.getId() + \":\" + resource.getName()));\n                }\n                return map;\n            }\n        };\n    }\n}\n"}, {"id": 71, "content": "- 管理员修改他人密码：set password for 'z1'@'localhost' = password('123')\n- 删除账号：drop user z2@localhost"}, {"id": 72, "content": "    @Override\n    public boolean isAccountNonLocked() {\n        return true;\n    }\n"}, {"id": 73, "content": "    @Override\n    public boolean isAccountNonLocked() {\n        return true;\n    }\n"}, {"id": 74, "content": "    @Override\n    public boolean isAccountNonLocked() {\n        return true;\n    }\n"}, {"id": 75, "content": "    public void setLoginTime(Date loginTime) {\n        this.loginTime = loginTime;\n    }\n"}, {"id": 76, "content": "    @Override\n    public void setAdmin(UmsAdmin admin) {\n        String key = REDIS_DATABASE + \":\" + REDIS_KEY_ADMIN + \":\" + admin.getUsername();\n        redisService.set(key, admin, REDIS_EXPIRE);\n    }\n"}, {"id": 77, "content": "    @Override\n    public UmsMember getCurrentMember() {\n        SecurityContext ctx = SecurityContextHolder.getContext();\n        Authentication auth = ctx.getAuthentication();\n        MemberDetails memberDetails = (MemberDetails) auth.getPrincipal();\n        return memberDetails.getUmsMember();\n    }\n"}, {"id": 78, "content": "    @Override\n    public Collection<? extends GrantedAuthority> getAuthorities() {\n        //返回当前用户的权限\n        return Arrays.asList(new SimpleGrantedAuthority(\"TEST\"));\n    }\n"}, {"id": 79, "content": "    @Override\n    public Collection<? extends GrantedAuthority> getAuthorities() {\n        //返回当前用户的权限\n        return Arrays.asList(new SimpleGrantedAuthority(\"TEST\"));\n    }\n"}, {"id": 80, "content": "    @Override\n    public String toString() {\n        StringBuilder sb = new StringBuilder();\n        sb.append(getClass().getSimpleName());\n        sb.append(\" [\");\n        sb.append(\"Hash = \").append(hashCode());\n        sb.append(\", id=\").append(id);\n        sb.append(\", username=\").append(username);\n        sb.append(\", password=\").append(password);\n        sb.append(\", icon=\").append(icon);\n        sb.append(\", email=\").append(email);\n        sb.append(\", nickName=\").append(nickName);\n        sb.append(\", note=\").append(note);\n        sb.append(\", createTime=\").append(createTime);\n        sb.append(\", loginTime=\").append(loginTime);\n        sb.append(\", status=\").append(status);\n        sb.append(\", serialVersionUID=\").append(serialVersionUID);\n        sb.append(\"]\");\n        return sb.toString();\n    }\n"}, {"id": 81, "content": "    public void setPriviledgeSignIn(Integer priviledgeSignIn) {\n        this.priviledgeSignIn = priviledgeSignIn;\n    }\n"}, {"id": 82, "content": "    @Override\n    public boolean isAccountNonExpired() {\n        return true;\n    }\n"}, {"id": 83, "content": "    @Override\n    public boolean isAccountNonExpired() {\n        return true;\n    }\n"}, {"id": 84, "content": "    @Override\n    public boolean isAccountNonExpired() {\n        return true;\n    }\n"}, {"id": 85, "content": "/**\n * 后台资源管理Service\n * Created by macro on 2020/2/2.\n */\npublic interface UmsResourceService {\n    /**\n     * 添加资源\n     */\n    int create(UmsResource umsResource);\n\n    /**\n     * 修改资源\n     */\n    int update(Long id, UmsResource umsResource);\n\n    /**\n     * 获取资源详情\n     */\n    UmsResource getItem(Long id);\n\n    /**\n     * 删除资源\n     */\n    int delete(Long id);\n\n    /**\n     * 分页查询资源\n     */\n    List<UmsResource> list(Long categoryId, String nameKeyword, String urlKeyword, Integer pageSize, Integer pageNum);\n\n    /**\n     * 查询全部资源\n     */\n    List<UmsResource> listAll();\n}\n"}, {"id": 86, "content": "    public Date getLoginTime() {\n        return loginTime;\n    }\n"}, {"id": 87, "content": "/**\n * 会员等级管理Controller\n * Created by macro on 2018/4/26.\n */\n@Controller\n@Api(tags = \"UmsMemberLevelController\")\n@Tag(name = \"UmsMemberLevelController\", description = \"会员等级管理\")\n@RequestMapping(\"/memberLevel\")\npublic class UmsMemberLevelController {\n    @Autowired\n    private UmsMemberLevelService memberLevelService;\n\n    @ApiOperation(\"查询所有会员等级\")\n    @RequestMapping(value = \"/list\", method = RequestMethod.GET)\n    @ResponseBody\n    public CommonResult<List<UmsMemberLevel>> list(@RequestParam(\"defaultStatus\") Integer defaultStatus) {\n        List<UmsMemberLevel> memberLevelList = memberLevelService.list(defaultStatus);\n        return CommonResult.success(memberLevelList);\n    }\n}\n"}, {"id": 88, "content": "/**\n * 后台菜单管理Service\n * Created by macro on 2020/2/2.\n */\npublic interface UmsMenuService {\n    /**\n     * 创建后台菜单\n     */\n    int create(UmsMenu umsMenu);\n\n    /**\n     * 修改后台菜单\n     */\n    int update(Long id, UmsMenu umsMenu);\n\n    /**\n     * 根据ID获取菜单详情\n     */\n    UmsMenu getItem(Long id);\n\n    /**\n     * 根据ID删除菜单\n     */\n    int delete(Long id);\n\n    /**\n     * 分页查询后台菜单\n     */\n    List<UmsMenu> list(Long parentId, Integer pageSize, Integer pageNum);\n\n    /**\n     * 树形结构返回所有菜单列表\n     */\n    List<UmsMenuNode> treeList();\n\n    /**\n     * 修改菜单显示状态\n     */\n    int updateHidden(Long id, Integer hidden);\n}\n"}, {"id": 89, "content": "  `nick_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '昵称',\n  `note` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注信息',\n  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',\n  `login_time` datetime NULL DEFAULT NULL COMMENT '最后登录时间',\n  `status` int(1) NULL DEFAULT 1 COMMENT '帐号启用状态：0->禁用；1->启用',\n  PRIMARY KEY (`id`) USING BTREE\n) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '后台用户表' ROW_FORMAT = DYNAMIC;\n\n-- ----------------------------\n-- Records of ums_admin\n-- ----------------------------\nINSERT INTO `ums_admin` VALUES (1, 'test', '$2a$10$NZ5o7r2E.ayT2ZoxgjlI.eJ6OEYqjH7INR/F.mXDbjZJi9HF0YCVG', 'https://macro-oss.oss-cn-shenzhen.aliyuncs.com/mall/icon/github_icon_02.png', '<EMAIL>', '测试账号', NULL, '2018-09-29 13:55:30', '2018-09-29 13:55:39', 1);\nINSERT INTO `ums_admin` VALUES (3, 'admin', '$2a$10$.E1FokumK5GIXWgKlg.Hc.i/0/2.qdAwYFL1zc5QHdyzpXOr38RZO', 'https://macro-oss.oss-cn-shenzhen.aliyuncs.com/mall/icon/github_icon_01.png', '<EMAIL>', '系统管理员', '系统管理员', '2018-10-08 13:32:47', '2019-04-20 12:45:16', 1);\n"}, {"id": 90, "content": "# mall 功能完成进度\n\n## 框架搭建\n\n功能 | 完成 \n----|----\n集成MyBatis | ✔\n集成MyBatisGenerator | ✔\n集成SpringSecurity | ✔\n集成Swagger-UI | ✔\n集成Hibernator-Validator | ✔\n集成日志功能 | ✔\n集成监控功能 | ✔\ncrud操作demo | ✔\n合理规划包结构 | ✔\nSpringAOP通用日志处理 | ✔\nSpringAOP通用验证失败结果返回 | ✔\nCommonResult对通用返回结果进行封装 | ✔\nSpringSecurity登录改为Restful形式 | ✔\nJWT登录、注册、获取token | ✔\nJTA事务处理 | ✔\n集成单元测试 | ✔\nOSS上传功能 | ✔\nElasticsearch搜索功能 | ✔\nHTTPS支持 | ✔\nRedis数字型ID生成 | ✔\nSpringTask定时任务支持 | ✔\ndocker容器化部署 | ✔\n配置区分生产和测试环境 | ✔\nELK日志收集功能 | ✔\nRabbitMq异步通信 | ✔\nRestTemplate服务间调用 | ✔\nSpringSecurity权限管理功能 | ✔\n集成SpringCloud |\n\n## 后台功能\n\n### 后台登录功能 \n\n- 后台用户注册功能\n- 后台用户登录后获取token\n"}, {"id": 91, "content": "/**\n * 用户注册参数\n * Created by macro on 2018/4/26.\n */\n@Getter\n@Setter\npublic class UmsAdminParam {\n    @NotEmpty\n    @ApiModelProperty(value = \"用户名\", required = true)\n    private String username;\n    @NotEmpty\n    @ApiModelProperty(value = \"密码\", required = true)\n    private String password;\n    @ApiModelProperty(value = \"用户头像\")\n    private String icon;\n    @Email\n    @ApiModelProperty(value = \"邮箱\")\n    private String email;\n    @ApiModelProperty(value = \"用户昵称\")\n    private String nickName;\n    @ApiModelProperty(value = \"备注\")\n    private String note;\n}\n"}, {"id": 92, "content": "/**\n * 会员等级管理Service实现类\n * Created by macro on 2018/4/26.\n */\n@Service\npublic class UmsMemberLevelServiceImpl implements UmsMemberLevelService{\n    @Autowired\n    private UmsMemberLevelMapper memberLevelMapper;\n    @Override\n    public List<UmsMemberLevel> list(Integer defaultStatus) {\n        UmsMemberLevelExample example = new UmsMemberLevelExample();\n        example.createCriteria().andDefaultStatusEqualTo(defaultStatus);\n        return memberLevelMapper.selectByExample(example);\n    }\n}\n"}, {"id": 93, "content": "/**\n * 用户地址管理Service\n * Created by macro on 2018/8/28.\n */\npublic interface UmsMemberReceiveAddressService {\n    /**\n     * 添加收货地址\n     */\n    int add(UmsMemberReceiveAddress address);\n\n    /**\n     * 删除收货地址\n     * @param id 地址表的id\n     */\n    int delete(Long id);\n\n    /**\n     * 修改收货地址\n     * @param id 地址表的id\n     * @param address 修改的收货地址信息\n     */\n    @Transactional\n    int update(Long id, UmsMemberReceiveAddress address);\n\n    /**\n     * 返回当前用户的收货地址\n     */\n    List<UmsMemberReceiveAddress> list();\n\n    /**\n     * 获取地址详情\n     * @param id 地址id\n     */\n    UmsMemberReceiveAddress getItem(Long id);\n}\n"}, {"id": 94, "content": "- 授予操作权限：grant select,insert on test.* to 'test'@'localhost' identified by '123'\n- 收回操作权限：revoke insert on test.* from 'test'@'localhost'\n\n## 其他\n\n### 字符集相关\n- 查看字符集：show variables like 'character%'\n- 创建数据库时指定字符集：create database mall character set utf8\n\n### 修改时区\n- 修改mysql全局时区为北京时间，即我们所在的东8区：set global time_zone = '+8:00';\n- 修改当前会话时区：set time_zone = '+8:00'\n- 立即生效：flush privileges\n\n### 权限相关\n- 授予所有数据库的所有权限：grant all privileges on *.* to z1@localhost identified by '123'\n- 授予所有数据库的所有权限(包括grant)：grant all privileges on *.* to z1@localhost with grant option\n- 授予SUPER PROCESS FILE权限：grant super,process,file on *.* to z3@localhost\n- 只授予登录权限：grant usage on *.* to z4@localhost\n- 查看账号权限：show grants for z1@localhost\n- 修改自己的密码：set password = password('123')\n- 管理员修改他人密码：set password for 'z1'@'localhost' = password('123')\n"}, {"id": 95, "content": "/**\n * 后台资源管理Service实现类\n * Created by macro on 2020/2/2.\n */\n@Service\npublic class UmsResourceServiceImpl implements UmsResourceService {\n    @Autowired\n    private UmsResourceMapper resourceMapper;\n    @Autowired\n    private UmsAdminCacheService adminCacheService;\n    @Override\n    public int create(UmsResource umsResource) {\n        umsResource.setCreateTime(new Date());\n        return resourceMapper.insert(umsResource);\n    }\n\n    @Override\n    public int update(Long id, UmsResource umsResource) {\n        umsResource.setId(id);\n        int count = resourceMapper.updateByPrimaryKeySelective(umsResource);\n        adminCacheService.delResourceListByResource(id);\n        return count;\n    }\n\n    @Override\n    public UmsResource getItem(Long id) {\n        return resourceMapper.selectByPrimaryKey(id);\n    }\n\n    @Override\n    public int delete(Long id) {\n        int count = resourceMapper.deleteByPrimaryKey(id);\n        adminCacheService.delResourceListByResource(id);\n        return count;\n    }\n\n    @Override\n    public List<UmsResource> list(Long categoryId, String nameKeyword, String urlKeyword, Integer pageSize, Integer pageNum) {\n        PageHelper.startPage(pageNum,pageSize);\n        UmsResourceExample example = new UmsResourceExample();\n        UmsResourceExample.Criteria criteria = example.createCriteria();\n        if(categoryId!=null){\n            criteria.andCategoryIdEqualTo(categoryId);\n        }\n        if(StrUtil.isNotEmpty(nameKeyword)){\n            criteria.andNameLike('%'+nameKeyword+'%');\n        }\n        if(StrUtil.isNotEmpty(urlKeyword)){\n            criteria.andUrlLike('%'+urlKeyword+'%');\n        }\n        return resourceMapper.selectByExample(example);\n    }\n\n    @Override\n    public List<UmsResource> listAll() {\n        return resourceMapper.selectByExample(new UmsResourceExample());\n    }\n}\n"}, {"id": 96, "content": "    public void setMemberUsername(String memberUsername) {\n        this.memberUsername = memberUsername;\n    }\n"}, {"id": 97, "content": "    public void setMemberUsername(String memberUsername) {\n        this.memberUsername = memberUsername;\n    }\n"}, {"id": 98, "content": "/**\n * 会员品牌关注管理Service\n * Created by macro on 2018/8/2.\n */\npublic interface MemberAttentionService {\n    /**\n     * 添加关注\n     */\n    int add(MemberBrandAttention memberBrandAttention);\n\n    /**\n     * 取消关注\n     */\n    int delete(Long brandId);\n\n    /**\n     * 分页获取会员关注列表\n     */\n    Page<MemberBrandAttention> list(Integer pageNum, Integer pageSize);\n\n    /**\n     * 获取会员关注详情\n     */\n    MemberBrandAttention detail(Long brandId);\n\n    /**\n     * 清空关注列表\n     */\n    void clear();\n}\n"}, {"id": 99, "content": "    public void setLoginType(Integer loginType) {\n        this.loginType = loginType;\n    }\n"}]}